{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 19v3\",\n  key: \"npa21l\"\n}], [\"path\", {\n  d: \"M12 2v3\",\n  key: \"qbqxhf\"\n}], [\"path\", {\n  d: \"M18.89 13.24a7 7 0 0 0-8.13-8.13\",\n  key: \"1v9jrh\"\n}], [\"path\", {\n  d: \"M19 12h3\",\n  key: \"osuazr\"\n}], [\"path\", {\n  d: \"M2 12h3\",\n  key: \"1wrr53\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M7.05 7.05a7 7 0 0 0 9.9 9.9\",\n  key: \"rc5l2e\"\n}]];\nconst LocateOff = createLucideIcon(\"locate-off\", __iconNode);\nexport { __iconNode, LocateOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "LocateOff", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\تجربة ديسك\\node_modules\\lucide-react\\src\\icons\\locate-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 19v3', key: 'npa21l' }],\n  ['path', { d: 'M12 2v3', key: 'qbqxhf' }],\n  ['path', { d: 'M18.89 13.24a7 7 0 0 0-8.13-8.13', key: '1v9jrh' }],\n  ['path', { d: 'M19 12h3', key: 'osuazr' }],\n  ['path', { d: 'M2 12h3', key: '1wrr53' }],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n  ['path', { d: 'M7.05 7.05a7 7 0 0 0 9.9 9.9', key: 'rc5l2e' }],\n];\n\n/**\n * @component @name LocateOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTl2MyIgLz4KICA8cGF0aCBkPSJNMTIgMnYzIiAvPgogIDxwYXRoIGQ9Ik0xOC44OSAxMy4yNGE3IDcgMCAwIDAtOC4xMy04LjEzIiAvPgogIDxwYXRoIGQ9Ik0xOSAxMmgzIiAvPgogIDxwYXRoIGQ9Ik0yIDEyaDMiIC8+CiAgPHBhdGggZD0ibTIgMiAyMCAyMCIgLz4KICA8cGF0aCBkPSJNNy4wNSA3LjA1YTcgNyAwIDAgMCA5LjkgOS45IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/locate-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LocateOff = createLucideIcon('locate-off', __iconNode);\n\nexport default LocateOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,kCAAoC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAU,GAC/D;AAaM,MAAAC,SAAA,GAAYC,gBAAiB,eAAcJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}