{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 6.5V3a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v3.5\",\n  key: \"sqyvz\"\n}], [\"path\", {\n  d: \"M9 18h8\",\n  key: \"i7pszb\"\n}], [\"path\", {\n  d: \"M18 3h-3\",\n  key: \"7idoqj\"\n}], [\"path\", {\n  d: \"M11 3a6 6 0 0 0-6 6v11\",\n  key: \"1v5je3\"\n}], [\"path\", {\n  d: \"M5 13h4\",\n  key: \"svpcxo\"\n}], [\"path\", {\n  d: \"M17 10a4 4 0 0 0-8 0v10a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2Z\",\n  key: \"vsjego\"\n}]];\nconst FireExtinguisher = createLucideIcon(\"fire-extinguisher\", __iconNode);\nexport { __iconNode, FireExtinguisher as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "FireExtinguisher", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\تجربة ديسك\\node_modules\\lucide-react\\src\\icons\\fire-extinguisher.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 6.5V3a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v3.5', key: 'sqyvz' }],\n  ['path', { d: 'M9 18h8', key: 'i7pszb' }],\n  ['path', { d: 'M18 3h-3', key: '7idoqj' }],\n  ['path', { d: 'M11 3a6 6 0 0 0-6 6v11', key: '1v5je3' }],\n  ['path', { d: 'M5 13h4', key: 'svpcxo' }],\n  ['path', { d: 'M17 10a4 4 0 0 0-8 0v10a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2Z', key: 'vsjego' }],\n];\n\n/**\n * @component @name FireExtinguisher\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgNi41VjNhMSAxIDAgMCAwLTEtMWgtMmExIDEgMCAwIDAtMSAxdjMuNSIgLz4KICA8cGF0aCBkPSJNOSAxOGg4IiAvPgogIDxwYXRoIGQ9Ik0xOCAzaC0zIiAvPgogIDxwYXRoIGQ9Ik0xMSAzYTYgNiAwIDAgMC02IDZ2MTEiIC8+CiAgPHBhdGggZD0iTTUgMTNoNCIgLz4KICA8cGF0aCBkPSJNMTcgMTBhNCA0IDAgMCAwLTggMHYxMGEyIDIgMCAwIDAgMiAyaDRhMiAyIDAgMCAwIDItMloiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/fire-extinguisher\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FireExtinguisher = createLucideIcon('fire-extinguisher', __iconNode);\n\nexport default FireExtinguisher;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAA,CAAS,GAC5E,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,wDAA0D;EAAAC,GAAA,EAAK;AAAU,GACzF;AAaM,MAAAC,gBAAA,GAAmBC,gBAAiB,sBAAqBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}