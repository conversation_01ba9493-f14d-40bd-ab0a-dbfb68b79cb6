{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 12h.01\",\n  key: \"1mp3jc\"\n}], [\"path\", {\n  d: \"M7.5 4.2c-.3-.5-.9-.7-1.3-.4C3.9 5.5 2.3 8.1 2 11c-.1.5.4 1 1 1h5c0-1.5.8-2.8 2-3.4-1.1-1.9-2-3.5-2.5-4.4z\",\n  key: \"wy49g3\"\n}], [\"path\", {\n  d: \"M21 12c.6 0 1-.4 1-1-.3-2.9-1.8-5.5-4.1-7.1-.4-.3-1.1-.2-1.3.3-.6.9-1.5 2.5-2.6 4.3 1.2.7 2 2 2 3.5h5z\",\n  key: \"vklnvr\"\n}], [\"path\", {\n  d: \"M7.5 19.8c-.3.5-.1 1.1.4 1.3 2.6 1.2 5.6 1.2 8.2 0 .5-.2.7-.8.4-1.3-.5-.9-1.4-2.5-2.5-4.3-1.2.7-2.8.7-4 0-1.1 1.8-2 3.4-2.5 4.3z\",\n  key: \"wkdf1o\"\n}]];\nconst Radiation = createLucideIcon(\"radiation\", __iconNode);\nexport { __iconNode, Radiation as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Radiation", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\تجربة ديسك\\node_modules\\lucide-react\\src\\icons\\radiation.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 12h.01', key: '1mp3jc' }],\n  [\n    'path',\n    {\n      d: 'M7.5 4.2c-.3-.5-.9-.7-1.3-.4C3.9 5.5 2.3 8.1 2 11c-.1.5.4 1 1 1h5c0-1.5.8-2.8 2-3.4-1.1-1.9-2-3.5-2.5-4.4z',\n      key: 'wy49g3',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M21 12c.6 0 1-.4 1-1-.3-2.9-1.8-5.5-4.1-7.1-.4-.3-1.1-.2-1.3.3-.6.9-1.5 2.5-2.6 4.3 1.2.7 2 2 2 3.5h5z',\n      key: 'vklnvr',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M7.5 19.8c-.3.5-.1 1.1.4 1.3 2.6 1.2 5.6 1.2 8.2 0 .5-.2.7-.8.4-1.3-.5-.9-1.4-2.5-2.5-4.3-1.2.7-2.8.7-4 0-1.1 1.8-2 3.4-2.5 4.3z',\n      key: 'wkdf1o',\n    },\n  ],\n];\n\n/**\n * @component @name Radiation\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTJoLjAxIiAvPgogIDxwYXRoIGQ9Ik03LjUgNC4yYy0uMy0uNS0uOS0uNy0xLjMtLjRDMy45IDUuNSAyLjMgOC4xIDIgMTFjLS4xLjUuNCAxIDEgMWg1YzAtMS41LjgtMi44IDItMy40LTEuMS0xLjktMi0zLjUtMi41LTQuNHoiIC8+CiAgPHBhdGggZD0iTTIxIDEyYy42IDAgMS0uNCAxLTEtLjMtMi45LTEuOC01LjUtNC4xLTcuMS0uNC0uMy0xLjEtLjItMS4zLjMtLjYuOS0xLjUgMi41LTIuNiA0LjMgMS4yLjcgMiAyIDIgMy41aDV6IiAvPgogIDxwYXRoIGQ9Ik03LjUgMTkuOGMtLjMuNS0uMSAxLjEuNCAxLjMgMi42IDEuMiA1LjYgMS4yIDguMiAwIC41LS4yLjctLjguNC0xLjMtLjUtLjktMS40LTIuNS0yLjUtNC4zLTEuMi43LTIuOC43LTQgMC0xLjEgMS44LTIgMy40LTIuNSA0LjN6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/radiation\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Radiation = createLucideIcon('radiation', __iconNode);\n\nexport default Radiation;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EACP,CAEJ;AAaM,MAAAC,SAAA,GAAYC,gBAAiB,cAAaJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}