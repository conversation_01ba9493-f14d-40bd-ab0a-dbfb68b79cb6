{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 17h1.5\",\n  key: \"1gkc67\"\n}], [\"path\", {\n  d: \"M12 22h1.5\",\n  key: \"1my7sn\"\n}], [\"path\", {\n  d: \"M12 2h1.5\",\n  key: \"19tvb7\"\n}], [\"path\", {\n  d: \"M17.5 22H19a1 1 0 0 0 1-1\",\n  key: \"10akbh\"\n}], [\"path\", {\n  d: \"M17.5 2H19a1 1 0 0 1 1 1v1.5\",\n  key: \"1vrfjs\"\n}], [\"path\", {\n  d: \"M20 14v3h-2.5\",\n  key: \"1naeju\"\n}], [\"path\", {\n  d: \"M20 8.5V10\",\n  key: \"1ctpfu\"\n}], [\"path\", {\n  d: \"M4 10V8.5\",\n  key: \"1o3zg5\"\n}], [\"path\", {\n  d: \"M4 19.5V14\",\n  key: \"ob81pf\"\n}], [\"path\", {\n  d: \"M4 4.5A2.5 2.5 0 0 1 6.5 2H8\",\n  key: \"s8vcyb\"\n}], [\"path\", {\n  d: \"M8 22H6.5a1 1 0 0 1 0-5H8\",\n  key: \"1cu73q\"\n}]];\nconst BookDashed = createLucideIcon(\"book-dashed\", __iconNode);\nexport { __iconNode, BookDashed as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "BookDashed", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\تجربة ديسك\\node_modules\\lucide-react\\src\\icons\\book-dashed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 17h1.5', key: '1gkc67' }],\n  ['path', { d: 'M12 22h1.5', key: '1my7sn' }],\n  ['path', { d: 'M12 2h1.5', key: '19tvb7' }],\n  ['path', { d: 'M17.5 22H19a1 1 0 0 0 1-1', key: '10akbh' }],\n  ['path', { d: 'M17.5 2H19a1 1 0 0 1 1 1v1.5', key: '1vrfjs' }],\n  ['path', { d: 'M20 14v3h-2.5', key: '1naeju' }],\n  ['path', { d: 'M20 8.5V10', key: '1ctpfu' }],\n  ['path', { d: 'M4 10V8.5', key: '1o3zg5' }],\n  ['path', { d: 'M4 19.5V14', key: 'ob81pf' }],\n  ['path', { d: 'M4 4.5A2.5 2.5 0 0 1 6.5 2H8', key: 's8vcyb' }],\n  ['path', { d: 'M8 22H6.5a1 1 0 0 1 0-5H8', key: '1cu73q' }],\n];\n\n/**\n * @component @name BookDashed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTdoMS41IiAvPgogIDxwYXRoIGQ9Ik0xMiAyMmgxLjUiIC8+CiAgPHBhdGggZD0iTTEyIDJoMS41IiAvPgogIDxwYXRoIGQ9Ik0xNy41IDIySDE5YTEgMSAwIDAgMCAxLTEiIC8+CiAgPHBhdGggZD0iTTE3LjUgMkgxOWExIDEgMCAwIDEgMSAxdjEuNSIgLz4KICA8cGF0aCBkPSJNMjAgMTR2M2gtMi41IiAvPgogIDxwYXRoIGQ9Ik0yMCA4LjVWMTAiIC8+CiAgPHBhdGggZD0iTTQgMTBWOC41IiAvPgogIDxwYXRoIGQ9Ik00IDE5LjVWMTQiIC8+CiAgPHBhdGggZD0iTTQgNC41QTIuNSAyLjUgMCAwIDEgNi41IDJIOCIgLz4KICA8cGF0aCBkPSJNOCAyMkg2LjVhMSAxIDAgMCAxIDAtNUg4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/book-dashed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookDashed = createLucideIcon('book-dashed', __iconNode);\n\nexport default BookDashed;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAED,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAU,GAC5D;AAaM,MAAAC,UAAA,GAAaC,gBAAiB,gBAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}