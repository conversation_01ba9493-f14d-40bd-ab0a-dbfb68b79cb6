{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"polygon\", {\n  points: \"5 4 15 12 5 20 5 4\",\n  key: \"16p6eg\"\n}], [\"line\", {\n  x1: \"19\",\n  x2: \"19\",\n  y1: \"5\",\n  y2: \"19\",\n  key: \"futhcm\"\n}]];\nconst SkipForward = createLucideIcon(\"skip-forward\", __iconNode);\nexport { __iconNode, SkipForward as default };", "map": {"version": 3, "names": ["__iconNode", "points", "key", "x1", "x2", "y1", "y2", "SkipForward", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\تجربة ديسك\\node_modules\\lucide-react\\src\\icons\\skip-forward.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['polygon', { points: '5 4 15 12 5 20 5 4', key: '16p6eg' }],\n  ['line', { x1: '19', x2: '19', y1: '5', y2: '19', key: 'futhcm' }],\n];\n\n/**\n * @component @name SkipForward\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjUgNCAxNSAxMiA1IDIwIDUgNCIgLz4KICA8bGluZSB4MT0iMTkiIHgyPSIxOSIgeTE9IjUiIHkyPSIxOSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/skip-forward\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SkipForward = createLucideIcon('skip-forward', __iconNode);\n\nexport default SkipForward;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,SAAW;EAAEC,MAAA,EAAQ,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAU,GACnE;AAaM,MAAAK,WAAA,GAAcC,gBAAiB,iBAAgBR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}