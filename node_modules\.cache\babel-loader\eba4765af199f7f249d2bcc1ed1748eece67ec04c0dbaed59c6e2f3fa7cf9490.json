{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2a10 10 0 0 1 7.38 16.75\",\n  key: \"175t95\"\n}], [\"path\", {\n  d: \"M12 8v8\",\n  key: \"napkw2\"\n}], [\"path\", {\n  d: \"M16 12H8\",\n  key: \"1fr5h0\"\n}], [\"path\", {\n  d: \"M2.5 8.875a10 10 0 0 0-.5 3\",\n  key: \"1vce0s\"\n}], [\"path\", {\n  d: \"M2.83 16a10 10 0 0 0 2.43 3.4\",\n  key: \"o3fkw4\"\n}], [\"path\", {\n  d: \"M4.636 5.235a10 10 0 0 1 .891-.857\",\n  key: \"1szpfk\"\n}], [\"path\", {\n  d: \"M8.644 21.42a10 10 0 0 0 7.631-.38\",\n  key: \"9yhvd4\"\n}]];\nconst CircleFadingPlus = createLucideIcon(\"circle-fading-plus\", __iconNode);\nexport { __iconNode, CircleFadingPlus as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "CircleFadingPlus", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\تجربة ديسك\\node_modules\\lucide-react\\src\\icons\\circle-fading-plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 2a10 10 0 0 1 7.38 16.75', key: '175t95' }],\n  ['path', { d: 'M12 8v8', key: 'napkw2' }],\n  ['path', { d: 'M16 12H8', key: '1fr5h0' }],\n  ['path', { d: 'M2.5 8.875a10 10 0 0 0-.5 3', key: '1vce0s' }],\n  ['path', { d: 'M2.83 16a10 10 0 0 0 2.43 3.4', key: 'o3fkw4' }],\n  ['path', { d: 'M4.636 5.235a10 10 0 0 1 .891-.857', key: '1szpfk' }],\n  ['path', { d: 'M8.644 21.42a10 10 0 0 0 7.631-.38', key: '9yhvd4' }],\n];\n\n/**\n * @component @name CircleFadingPlus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMmExMCAxMCAwIDAgMSA3LjM4IDE2Ljc1IiAvPgogIDxwYXRoIGQ9Ik0xMiA4djgiIC8+CiAgPHBhdGggZD0iTTE2IDEySDgiIC8+CiAgPHBhdGggZD0iTTIuNSA4Ljg3NWExMCAxMCAwIDAgMC0uNSAzIiAvPgogIDxwYXRoIGQ9Ik0yLjgzIDE2YTEwIDEwIDAgMCAwIDIuNDMgMy40IiAvPgogIDxwYXRoIGQ9Ik00LjYzNiA1LjIzNWExMCAxMCAwIDAgMSAuODkxLS44NTciIC8+CiAgPHBhdGggZD0iTTguNjQ0IDIxLjQyYTEwIDEwIDAgMCAwIDcuNjMxLS4zOCIgLz4KPC9zdmc+) - https://lucide.dev/icons/circle-fading-plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleFadingPlus = createLucideIcon('circle-fading-plus', __iconNode);\n\nexport default CircleFadingPlus;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,6BAA+B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5D,CAAC,MAAQ;EAAED,CAAA,EAAG,+BAAiC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,MAAQ;EAAED,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,MAAQ;EAAED,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAU,GACrE;AAaM,MAAAC,gBAAA,GAAmBC,gBAAiB,uBAAsBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}