{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 4v16\",\n  key: \"vw9hq8\"\n}], [\"path\", {\n  d: \"M2 8h18a2 2 0 0 1 2 2v10\",\n  key: \"1dgv2r\"\n}], [\"path\", {\n  d: \"M2 17h20\",\n  key: \"18nfp3\"\n}], [\"path\", {\n  d: \"M6 8v9\",\n  key: \"1yriud\"\n}]];\nconst Bed = createLucideIcon(\"bed\", __iconNode);\nexport { __iconNode, Bed as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Bed", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\تجربة ديسك\\node_modules\\lucide-react\\src\\icons\\bed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M2 4v16', key: 'vw9hq8' }],\n  ['path', { d: 'M2 8h18a2 2 0 0 1 2 2v10', key: '1dgv2r' }],\n  ['path', { d: 'M2 17h20', key: '18nfp3' }],\n  ['path', { d: 'M6 8v9', key: '1yriud' }],\n];\n\n/**\n * @component @name Bed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiA0djE2IiAvPgogIDxwYXRoIGQ9Ik0yIDhoMThhMiAyIDAgMCAxIDIgMnYxMCIgLz4KICA8cGF0aCBkPSJNMiAxN2gyMCIgLz4KICA8cGF0aCBkPSJNNiA4djkiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/bed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Bed = createLucideIcon('bed', __iconNode);\n\nexport default Bed;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAU,GACzC;AAaM,MAAAC,GAAA,GAAMC,gBAAiB,QAAOJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}