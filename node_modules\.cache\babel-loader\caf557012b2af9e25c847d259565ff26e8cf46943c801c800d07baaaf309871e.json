{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"16\",\n  height: \"20\",\n  x: \"4\",\n  y: \"2\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"76otgf\"\n}], [\"path\", {\n  d: \"M9 22v-4h6v4\",\n  key: \"r93iot\"\n}], [\"path\", {\n  d: \"M8 6h.01\",\n  key: \"1dz90k\"\n}], [\"path\", {\n  d: \"M16 6h.01\",\n  key: \"1x0f13\"\n}], [\"path\", {\n  d: \"M12 6h.01\",\n  key: \"1vi96p\"\n}], [\"path\", {\n  d: \"M12 10h.01\",\n  key: \"1nrarc\"\n}], [\"path\", {\n  d: \"M12 14h.01\",\n  key: \"1etili\"\n}], [\"path\", {\n  d: \"M16 10h.01\",\n  key: \"1m94wz\"\n}], [\"path\", {\n  d: \"M16 14h.01\",\n  key: \"1gbofw\"\n}], [\"path\", {\n  d: \"M8 10h.01\",\n  key: \"19clt8\"\n}], [\"path\", {\n  d: \"M8 14h.01\",\n  key: \"6423bh\"\n}]];\nconst Building = createLucideIcon(\"building\", __iconNode);\nexport { __iconNode, Building as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "ry", "key", "d", "Building", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\تجربة ديسك\\node_modules\\lucide-react\\src\\icons\\building.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '16', height: '20', x: '4', y: '2', rx: '2', ry: '2', key: '76otgf' }],\n  ['path', { d: 'M9 22v-4h6v4', key: 'r93iot' }],\n  ['path', { d: 'M8 6h.01', key: '1dz90k' }],\n  ['path', { d: 'M16 6h.01', key: '1x0f13' }],\n  ['path', { d: 'M12 6h.01', key: '1vi96p' }],\n  ['path', { d: 'M12 10h.01', key: '1nrarc' }],\n  ['path', { d: 'M12 14h.01', key: '1etili' }],\n  ['path', { d: 'M16 10h.01', key: '1m94wz' }],\n  ['path', { d: 'M16 14h.01', key: '1gbofw' }],\n  ['path', { d: 'M8 10h.01', key: '19clt8' }],\n  ['path', { d: 'M8 14h.01', key: '6423bh' }],\n];\n\n/**\n * @component @name Building\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMjAiIHg9IjQiIHk9IjIiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNOSAyMnYtNGg2djQiIC8+CiAgPHBhdGggZD0iTTggNmguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDZoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiA2aC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTBoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiAxNGguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNMTYgMTRoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNOCAxNGguMDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/building\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Building = createLucideIcon('building', __iconNode);\n\nexport default Building;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,KAAO;EAAMC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG;EAAKC,EAAI;EAAKC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvF,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAU,GAC5C;AAaM,MAAAE,QAAA,GAAWC,gBAAiB,aAAYV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}